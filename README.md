# Image Attribute Extractor

A Python module for extracting structured product attributes from catalog images using OpenAI's GPT-4 Vision API.

## Features

- Extract comprehensive product attributes from images
- Robust error handling for network requests and API calls
- JSON validation and parsing
- Proper logging and debugging information
- Type hints for better code maintainability

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set your OpenAI API key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

## Usage

### Basic Usage

```python
from image_attribute_extractor import extract_image_attributes

# Extract attributes from an image URL
image_url = "https://example.com/product-image.jpg"
attributes = extract_image_attributes(image_url)
print(attributes)
```

### With Product Description

```python
from image_attribute_extractor import extract_image_attributes

image_url = "https://example.com/product-image.jpg"
description = "Cotton t-shirt with logo print"

attributes = extract_image_attributes(image_url, description)
print(attributes)
```

## Extracted Attributes

The module extracts the following types of attributes:

### General Product Attributes
- `product_type`: e.g., sneaker, backpack, chair, phone
- `brand`: Visible logo or inferred brand
- `color`: Dominant colors
- `material`: Fabric, plastic, leather, metal, etc.
- `size`: If visible (e.g., "32-inch", "M", "500ml")
- `dimensions`: If printed on product
- `shape`: Basic shape or geometry
- `design_features`: Notable design elements
- `condition`: new/used/refurbished

### Label/Text Extraction (OCR)
- `text_content`: Full string detected
- `font_style`: e.g., bold, serif, cursive
- `position`: Bounding box or placement
- `language`: Language of the text
- `purpose`: e.g., brand name, size info, SKU

### Image Context
- `background_type`: e.g., white studio background, lifestyle setting
- `lighting_quality`: e.g., well-lit, overexposed, shadows
- `image_quality`: high, medium, low
- `number_of_products`: 1 or multiple
- `obstructions`: visible obstructions (tags, stickers, watermarks)

## Error Handling

The module includes comprehensive error handling for:
- Invalid or missing image URLs
- Network connectivity issues
- API authentication errors
- Invalid JSON responses
- Image download failures

## Testing

Run the test suite:
```bash
python test_extractor.py
```

## Requirements

- Python 3.7+
- requests>=2.31.0
- openai>=1.0.0
- Valid OpenAI API key

## Recent Fixes

- ✅ Fixed improper OpenAI API usage (removed invalid `additional_context` field)
- ✅ Added comprehensive error handling for network requests and API calls
- ✅ Integrated attribute specification from comments into the actual prompt
- ✅ Added JSON response validation and parsing
- ✅ Improved prompt structure to be more specific and actionable
- ✅ Added proper logging and removed debug prints
- ✅ Added input validation for URLs and parameters
- ✅ Fixed lazy initialization of OpenAI client to prevent import-time errors
- ✅ Added type hints for better code maintainability
- ✅ Improved code organization and documentation
