import requests
import base64

import os
from openai import OpenAI

client = OpenAI(
    # This is the default and can be omitted
    api_key=os.environ.get("OPENAI_API_KEY"),
)


def load_image_from_url(image_url):
    """Download an image from a URL and convert to base64."""
    print("extracting image from url: " + image_url)
    response = requests.get(image_url)
    response.raise_for_status()  # Ensure we got a valid response
    image_data = response.content
    print("done extracting image from url: " + image_url)
    return base64.b64encode(image_data).decode('utf-8')


# Detailed prompt for attribute extraction
detailed_prompt = """
You are an expert AI assistant for extracting structured product attributes from catalog images.
Given a product image, extract all possible attributes to help automate catalog entry or search metadata.
Your output must be comprehensive, accurate, and in JSON format. Handle variations and edge cases gracefully.


Return JSON only, with no extra text or explanation.
"""


def extract_image_attributes(image_url=None, product_description=None):
    # Use default URL if none provided
    if image_url is None:
        image_url = "https://example.com/default_image.jpg"  # Set your default image URL here

    # Prepare content list for the user message
    user_content = [{"type": "text", "text": detailed_prompt}]

    # Add image with optional product description
    image_content = {
        "type": "image_url",
        "image_url": {
            "url": f"data:image/jpeg;base64,{load_image_from_url(image_url)}"
        }
    }

    # Add product description if provided
    if product_description:
        image_content["additional_context"] = {
            "product_details": product_description
        }

    user_content.append(image_content)

    # Call GPT-4o with vision support
    completion = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": "You are a helpful assistant that extracts attributes from product catalog images."
            },
            {
                "role": "user",
                "content": user_content
            }
        ],
        max_tokens=1500,
        temperature=0.2
    )

    return completion.choices[0].message.content


# Example usage
if __name__ == "__main__":
    image_url = "https://www.chicos.com/_assets/products/content/wahpj13hrd/jpeg/CHS_WAVY%20STITCH%20TANK_570393658_GRN006788_OF_MAIN.jpeg?position=c&color=ffffffff&quality=100&u=9o1w1f&versionId=b2bdc58d"
    image_url = "https://www.chicos.com/_assets/products/content/mvaeowj5zq/jpeg/CHS_RUCHED%20FRONT%20TEE_570392805_BLK000001_OF_MAIN.jpeg?position=c&color=ffffffff&quality=100&u=9o1w1f&versionId=6f336ffd"
    image_url = "https://www.chicos.com/_assets/products/content/02j9wkvhfq/jpeg/CHS_HIGH%20NECK%20RIB%20TANK_570365421_PUR006759_OF_MAIN.jpeg"
    image_url = "https://m.media-amazon.com/images/I/71j3jWHTaxL._SY879_.jpg"
    image_url = "https://www.chicos.com/_assets/products/content/jjio8cohw8/jpeg/CHS_SYCAMORE%20STRIPE_570390181_NEU004540_OF_MAIN.jpeg"

    description = """
    Woven with airy linen and cut for a classic fit, this popover shirt is detailed with shirring at the shoulder and finished with a classy, neutral-thick striped print. Short subtly fluttery sleeves and a split neckline, this is the perfect shirt for all season long.
        Linen-rayon blend fabrication.
        Classic-fit, short-sleeved linen shirt.
        Length: 27".
        54% Linen, 1% Metallic, 45% Rayon.
        Machine wash cold.
        Imported.
    """

    # Can be called with or without an image URL
    result = extract_image_attributes(image_url, description)  # Uses default
    # Or: result = extract_image_attributes("https://example.com/product.jpg")
    print(result)

# Extract the following attributes from the image:
#
# General Product Attributes:
# - product_type: e.g., sneaker, backpack, chair, phone
# - brand: Visible logo or inferred brand
# - color: Dominant colors (e.g., "red", "navy blue", "multi-colored")
# - material: Fabric, plastic, leather, metal, etc.
# - size: If visible (e.g., "32-inch", "M", "500ml")
# - dimensions: If printed on product (H×W×D or L×B×H)
# - shape: Basic shape or geometry (e.g., round, rectangular)
# - design_features: Notable design elements (e.g., "zipper front", "embossed logo", "mesh panels")
# - condition: new/used/refurbished (based on wear or packaging)
#
# Label/Text Extraction (OCR):
# For any visible text (e.g., printed labels, tags, packaging):
# - text_content: The full string detected
# - font_style: e.g., bold, serif, cursive
# - position: Bounding box or placement (top-left, center, etc.)
# - language: Language of the text
# - purpose: e.g., brand name, size info, SKU, safety warning
#
# Image Context (optional, helps with automated quality checking):
# - background_type: e.g., white studio background, lifestyle setting
# - lighting_quality: e.g., well-lit, overexposed, shadows
# - image_quality: high, medium, low (based on clarity)
# - number_of_products: 1 or multiple (if multiple, describe briefly)
# - obstructions: any visible obstructions (tags, stickers, watermarks, etc.)
#
# Edge Cases and Error Handling:
# - If the product is partially visible, return "partial_visibility": true
# - If any attribute is unclear, set its value as "unknown" and add "confidence_score": <0.0 - 1.0>
# - If multiple possibilities exist (e.g., "color": ["black", "dark gray"]), list all with scores
