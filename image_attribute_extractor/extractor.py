import requests
import base64
import json
import os
import logging
from typing import Optional, Dict, Any
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global client variable - will be initialized when needed
client = None

def get_openai_client():
    """Get or create OpenAI client instance."""
    global client
    if client is None:
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        client = OpenAI(api_key=api_key)
    return client


def load_image_from_url(image_url: str) -> str:
    """Download an image from a URL and convert to base64.

    Args:
        image_url: The URL of the image to download

    Returns:
        Base64 encoded string of the image

    Raises:
        requests.RequestException: If the image cannot be downloaded
        ValueError: If the URL is invalid
    """
    if not image_url or not image_url.startswith(('http://', 'https://')):
        raise ValueError(f"Invalid image URL: {image_url}")

    logger.info(f"Downloading image from URL: {image_url}")

    try:
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()
        image_data = response.content

        if len(image_data) == 0:
            raise ValueError("Downloaded image is empty")

        logger.info(f"Successfully downloaded image ({len(image_data)} bytes)")
        return base64.b64encode(image_data).decode('utf-8')

    except requests.RequestException as e:
        logger.error(f"Failed to download image from {image_url}: {e}")
        raise


# Detailed prompt for attribute extraction
detailed_prompt = """
You are an expert AI assistant for extracting structured product attributes from catalog images.
Given a product image, extract all possible attributes to help automate catalog entry or search metadata.
Your output must be comprehensive, accurate, and in JSON format. Handle variations and edge cases gracefully.

Return ONLY valid JSON with no extra text or explanation.
"""


def extract_image_attributes(image_url: Optional[str] = None, product_description: Optional[str] = None) -> Dict[str, Any]:
    """
    Extract structured attributes from a product image using OpenAI's GPT-4 Vision API.

    Args:
        image_url: URL of the product image to analyze
        product_description: Optional additional context about the product

    Returns:
        Dictionary containing extracted product attributes

    Raises:
        ValueError: If no image URL is provided or if the API response is invalid
        Exception: If the API call fails
    """
    if image_url is None:
        raise ValueError("Image URL is required")

    try:
        # Build the prompt with optional product description
        prompt_text = detailed_prompt
        if product_description:
            prompt_text += f"\n\nAdditional Product Context:\n{product_description}"

        # Prepare content list for the user message
        user_content = [{"type": "text", "text": prompt_text}]

        # Add image content
        image_content = {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{load_image_from_url(image_url)}"
            }
        }
        user_content.append(image_content)

        logger.info("Calling OpenAI API for image attribute extraction")

        # Call GPT-4o with vision support
        openai_client = get_openai_client()
        completion = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant that extracts attributes from product catalog images. Always return valid JSON."
                },
                {
                    "role": "user",
                    "content": user_content
                }
            ],
            max_tokens=1500,
            temperature=0.2
        )

        response_content = completion.choices[0].message.content
        logger.info("Received response from OpenAI API")

        # Validate and parse JSON response
        try:
            parsed_response = json.loads(response_content)
            return parsed_response
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response_content}")
            # Try to extract JSON from response if it contains extra text
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                try:
                    parsed_response = json.loads(json_match.group())
                    return parsed_response
                except json.JSONDecodeError:
                    pass

            raise ValueError(f"API returned invalid JSON: {response_content}")

    except Exception as e:
        logger.error(f"Error in extract_image_attributes: {e}")
        raise


# Example usage
if __name__ == "__main__":
    # Example image URLs for testing
    test_urls = [
        "https://www.superkicks.in/cdn/shop/files/4_0412d651-8ed0-4653-8f1b-308222b0bf04.jpg"
    ]

    # Use the last URL for this example
    image_url = test_urls[-1]

    description = """
    Carve a new lane for yourself in the Zoom Vomero 5—your go-to for complexity, depth and easy styling. The richly layered design includes textiles, leather and plastic accents that come together to make one of the coolest sneakers of the season.
Upper mixes real and synthetic leather for a layered look built to last.
Mesh panels and ventilation ports on the heel keep it light and breathable.
Snappy and responsive, Zoom Air cushioning helps provide a quick-off-the-ground sensation.
Plastic caging on the side creates a supportive feel.
Rubber outsole gives you durable traction.
    """

    try:
        # Extract attributes with product description
        result = extract_image_attributes(image_url, description)
        print("Extracted attributes:")
        print(json.dumps(result, indent=2))

    except Exception as e:
        logger.error(f"Failed to extract attributes: {e}")
        print(f"Error: {e}")

"""
title:""
image:""
description:""
Attributes: {}
"""
{
original:
generated:{
    value:""
    metadata: {
        reasoning:
        existing/new:
    }
}


}

