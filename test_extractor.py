#!/usr/bin/env python3
"""
Simple test script to verify the image attribute extractor works correctly.
"""

import os
import sys
from image_attribute_extractor import extract_image_attributes

def test_basic_functionality():
    """Test basic functionality with error handling."""
    
    # Test 1: Invalid URL should raise ValueError
    print("Test 1: Testing invalid URL handling...")
    try:
        result = extract_image_attributes("invalid-url")
        print("❌ Should have raised ValueError for invalid URL")
        return False
    except ValueError as e:
        print(f"✅ Correctly caught invalid URL: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test 2: None URL should raise ValueError
    print("\nTest 2: Testing None URL handling...")
    try:
        result = extract_image_attributes(None)
        print("❌ Should have raised ValueError for None URL")
        return False
    except ValueError as e:
        print(f"✅ Correctly caught None URL: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test 3: Check if OpenAI API key is set
    print("\nTest 3: Checking OpenAI API key...")
    if not os.environ.get("OPENAI_API_KEY"):
        print("⚠️  OPENAI_API_KEY not set - skipping API tests")
        return True
    else:
        print("✅ OpenAI API key is set")
    
    # Test 4: Test with a real image URL (if API key is available)
    print("\nTest 4: Testing with real image URL...")
    test_image_url = "https://m.media-amazon.com/images/I/71j3jWHTaxL._SY879_.jpg"
    
    try:
        result = extract_image_attributes(test_image_url)
        print("✅ Successfully extracted attributes")
        print(f"Result type: {type(result)}")
        if isinstance(result, dict):
            print(f"Number of attributes: {len(result)}")
            # Print first few keys
            keys = list(result.keys())[:5]
            print(f"Sample keys: {keys}")
        else:
            print(f"❌ Expected dict, got {type(result)}")
            return False
        return True
        
    except Exception as e:
        print(f"❌ Error during API call: {e}")
        return False

if __name__ == "__main__":
    print("Testing Image Attribute Extractor...")
    print("=" * 50)
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
